{"name": "test_login[\\u5bc6\\u7801\\u9519\\u8bef-req_data1-200-\\u5bc6\\u7801\\u9519\\u8bef--4]", "status": "passed", "parameters": [{"name": "desc", "value": "'密码错误'"}, {"name": "req_data", "value": "{'accounts': 'huace_xm', 'pwd': 'wrong_password', 'type': 'username'}"}, {"name": "status_code", "value": "200"}, {"name": "msg", "value": "'密码错误'"}, {"name": "code", "value": "-4"}], "start": *************, "stop": *************, "uuid": "7f8137b6-**************-96248468f73d", "historyId": "077431f1df66fdeb9ec6ea86600ca24f", "testCaseId": "1a090559fce0c0113352995e14278930", "fullName": "scripts.test_login_params.TestLoginParams#test_login", "labels": [{"name": "parentSuite", "value": "scripts"}, {"name": "suite", "value": "test_login_params"}, {"name": "subSuite", "value": "TestLoginParams"}, {"name": "host", "value": "Li"}, {"name": "thread", "value": "4796-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "scripts.test_login_params"}]}