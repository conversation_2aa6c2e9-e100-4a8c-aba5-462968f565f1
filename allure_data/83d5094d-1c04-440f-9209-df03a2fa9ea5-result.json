{"name": "test_login[\\u767b\\u5f55\\u6210\\u529f-req_data0-200-\\u767b\\u5f55\\u6210\\u529f-0]", "status": "passed", "parameters": [{"name": "desc", "value": "'登录成功'"}, {"name": "req_data", "value": "{'accounts': 'huace_xm', 'pwd': '123456', 'type': 'username'}"}, {"name": "status_code", "value": "200"}, {"name": "msg", "value": "'登录成功'"}, {"name": "code", "value": "0"}], "start": *************, "stop": *************, "uuid": "d5072591-5d70-4af4-97cd-546d62d97ef6", "historyId": "5e345c6ce4d0f10817fc2b6ea3a135da", "testCaseId": "1a090559fce0c0113352995e14278930", "fullName": "scripts.test_login_params.TestLoginParams#test_login", "labels": [{"name": "parentSuite", "value": "scripts"}, {"name": "suite", "value": "test_login_params"}, {"name": "subSuite", "value": "TestLoginParams"}, {"name": "host", "value": "Li"}, {"name": "thread", "value": "4796-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "scripts.test_login_params"}]}