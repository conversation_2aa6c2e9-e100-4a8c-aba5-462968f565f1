import requests

# 测试不同的账号
test_accounts = [
    {"accounts": "admin123", "pwd": "123456", "type": "username"},
    {"accounts": "testuser", "pwd": "123456", "type": "username"},
    {"accounts": "huace_xm", "pwd": "123456", "type": "username"},
    {"accounts": "test123", "pwd": "123456", "type": "username"},
    {"accounts": "demo123", "pwd": "123456", "type": "username"},
]

url = "http://shop-xo.hctestedu.com/index.php?s=api/user/login"
headers = {"application": "app", "application_client_type": "weixin"}
params = {"application": "app", "application_client_type": "weixin"}

print("测试不同的登录账号...")
for i, data in enumerate(test_accounts, 1):
    try:
        resp = requests.post(url=url, headers=headers, params=params, data=data)
        result = resp.json()
        print(f"{i}. 账号: {data['accounts']}, 密码: {data['pwd']}")
        print(f"   响应: {result}")
        print(f"   状态: {'成功' if result.get('code') == 0 else '失败'}")
        print("-" * 50)
    except Exception as e:
        print(f"{i}. 账号: {data['accounts']}, 错误: {e}")
        print("-" * 50)
