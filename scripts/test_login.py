from api import hc_login_api
from api.hc_login_api import LoginApi
from common.assert_tools import common_assert


class TestLogin(object):
    def test_login(self):
        req_data = {"accounts": "huace_xm", "pwd": "123456", "type": "username"}
        resp = LoginApi.login(req_data)
        print("登录成功:", resp.json())
        common_assert(resp,200,'登录成功',0)
        # assert 200 == resp.status_code
        # assert "登录成功" == resp.json().get("msg")
        # assert 0 == resp.json()["code"]

    # def test02_login_failure(self):
    #     req_data = {"accounts": "123456", "pwd": "123456q", "type": "username"}
    #     resp = LoginApi.login(req_data)
    #     print("登录失败:", resp.json())
    #     common_assert(resp, 200, '密码错误', -4)
    #     assert 200 == resp.status_code
    #     assert "密码错误"==resp.json()["msg"]
    #     assert -4 == resp.json()["code"]