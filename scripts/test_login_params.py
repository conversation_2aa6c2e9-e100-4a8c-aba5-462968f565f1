import pytest

from api import hc_login_api
from api.hc_login_api import LoginApi
from common.assert_tools import common_assert
from common.read_json_file import read_json_file
from config import BASE_DIR


class TestLoginParams(object):
    data = read_json_file(BASE_DIR + '/data/login_data.json')

    @pytest.mark.parametrize("desc,req_data,status_code,msg,code",data)
    def test_login(self,desc,req_data,status_code,msg,code):
        resp = LoginApi.login(req_data)
        print(desc,"：", resp.json())
        common_assert(resp, status_code, msg, code)
        # assert 200 == resp.status_code
        # assert "登录成功" == resp.json().get("msg")
        # assert 0 == resp.json()["code"]

    # def test02_login_failure(self):
    #     req_data = {"accounts": "123456", "pwd": "123456q", "type": "username"}
    #     resp = LoginApi.login(req_data)
    #     print("登录失败:", resp.json())
    #     common_assert(resp, 200, '密码错误', -4)
    # assert 200 == resp.status_code
    # assert "密码错误"==resp.json()["msg"]
    # assert -4 == resp.json()["code"]
    #1111